---
import LanguageSwitcher from "../common/LanguageSwitcher.astro";
import ThemeToggle from "../common/ThemeToggle.astro";

interface Props {
  githubUsername?: string;
  i18n: any;
  lang: "fr" | "en";
  initialOpen?: boolean;
}

const {
  githubUsername = "Jamedie",
  lang = [
    { code: "fr", label: "Français" },
    { code: "en", label: "English" },
  ],
  initialOpen = false,
} = Astro.props as Props;

const LANG_JSON = JSON.stringify(lang);
---

<!--
QuickAccessMenu.astro
• Menu plein écran qui bloque le scroll
• Thème: clair/sombre/système (persisté)
• Langue: liste dynamique via props
• Activité GitHub publique (10 derniers events)
• Ra<PERSON><PERSON><PERSON> en bas à gauche (Ctrl/⌘+K, T, L, Échap)


Utilisation:
<QuickAccessMenu githubUsername="tonUser" languages={[{code:'fr',label:'Français'},{code:'en',label:'English'}]} />


Styles: autonome via <style> ci-dessous (pas besoin de Tailwind/React)
-->
<section class="quick-access-menu">
  <div
    id="qa-root"
    class="qa-root"
    data-open={initialOpen ? "1" : "0"}
    data-lang={lang || "fr"}
  >
    <div class="qa-overlay" data-qa-overlay hidden></div>
    <div
      class="qa-panel"
      role="dialog"
      aria-modal="true"
      aria-label="Quick Access"
      data-qa-panel
      hidden
    >
      <header class="qa-header">
        <div class="qa-title">
          <strong>Quick Access</strong>
          <span class="qa-sub">Appuyez sur Échap pour fermer</span>
        </div>
        <button class="qa-icon-btn" aria-label="Fermer" data-qa-close>✕</button>
      </header>

      <main class="qa-grid">
        <!-- Colonne 1: Thème -->
        <section class="qa-card">
          <div class="qa-card-title">🌓 Apparence</div>
          <div class="qa-row">
            <button class="qa-chip" data-qa-theme="light">Clair</button>
            <button class="qa-chip" data-qa-theme="dark">Sombre</button>
            <button class="qa-chip" data-qa-theme="system">Système</button>
          </div>
          <p class="qa-hint">
            Astuce : appuyez sur <kbd>T</kbd> pour basculer rapidement.
          </p>
        </section>

        <!-- Colonne 2: Langue -->
        <section class="qa-lang">
          <div class="qa-card-title">{i18n.quickAccess.language}</div>
          <LanguageSwitcher lang={lang} />
        </section>

        <!-- Colonne 3: GitHub -->
        <section class="qa-card">
          <div class="qa-card-title">🐙 Activité GitHub</div>
          <div class="qa-gh" data-qa-gh>
            <p class="qa-muted">
              {
                githubUsername
                  ? "Chargement…"
                  : "Aucun utilisateur GitHub configuré."
              }
            </p>
          </div>
        </section>
      </main>

      <!-- Raccourcis bas-gauche -->
      <div class="qa-shortcuts">
        <div class="qa-short">
          <span>Ouvrir/fermer</span><span class="qa-keys"
            ><kbd>Ctrl/⌘</kbd><kbd>K</kbd></span
          >
        </div>
        <div class="qa-short">
          <span>Basculer thème</span><span class="qa-keys"><kbd>T</kbd></span>
        </div>
        <div class="qa-short">
          <span>Changer langue</span><span class="qa-keys"><kbd>L</kbd></span>
        </div>
        <div class="qa-short">
          <span>Fermer</span><span class="qa-keys"><kbd>Échap</kbd></span>
        </div>
      </div>
    </div>
  </div>
</section>

<style>
  .qa-root {
    position: relative;
    font-family:
      ui-sans-serif,
      system-ui,
      -apple-system,
      Segoe UI,
      Roboto,
      Ubuntu,
      Cantarell,
      Noto Sans,
      Helvetica Neue,
      Arial,
      "Apple Color Emoji",
      "Segoe UI Emoji";
  }

  .qa-fab {
    position: fixed;
    right: 20px;
    bottom: 20px;
    z-index: 40;
    display: flex;
    gap: 0.5rem;
    align-items: center;
    border: 1px solid var(--qa-border);
    background: var(--qa-fg);
    color: var(--qa-bg);
    border-radius: 1rem;
    padding: 0.75rem 1rem;
    box-shadow: var(--qa-elev);
    cursor: pointer;
  }
  .qa-fab:hover {
    opacity: 0.9;
  }
  .qa-fab .qa-kbd {
    font-size: 0.75rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 0.1rem 0.35rem;
    border-radius: 0.375rem;
    opacity: 0.8;
  }
  .qa-fab .qa-fab-text {
    font-weight: 600;
    font-size: 0.9rem;
  }

  .qa-overlay {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.55);
    backdrop-filter: blur(2px);
    z-index: 49;
  }
  .qa-panel {
    position: fixed;
    inset: 3rem auto auto 50%;
    transform: translateX(-50%);
    width: min(1100px, 92vw);
    height: calc(100dvh - 6rem);
    background: var(--qa-panel);
    color: var(--qa-fg);
    border: 1px solid var(--qa-border);
    border-radius: 1.25rem;
    box-shadow: var(--qa-elev);
    z-index: 50;
    display: flex;
    flex-direction: column;
  }
  .qa-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.25rem;
    border-bottom: 1px solid var(--qa-border);
  }
  .qa-title {
    display: flex;
    align-items: baseline;
    gap: 0.75rem;
  }
  .qa-title strong {
    font-size: 1.1rem;
  }
  .qa-sub {
    font-size: 0.75rem;
    color: var(--qa-muted);
  }
  .qa-icon-btn {
    border: 1px solid var(--qa-border);
    background: transparent;
    color: var(--qa-fg);
    border-radius: 0.75rem;
    padding: 0.4rem 0.6rem;
    cursor: pointer;
  }
  .qa-icon-btn:hover {
    background: rgba(0, 0, 0, 0.05);
  }

  .qa-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 1rem;
    overflow: auto;
    height: 100%;
  }
  @media (min-width: 768px) {
    .qa-grid {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  .qa-card {
    border: 1px solid var(--qa-border);
    border-radius: 1rem;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }
  .qa-card-title {
    font-weight: 600;
  }
  .qa-row {
    display: flex;
    gap: 0.5rem;
  }
  .qa-chip {
    flex: 1;
    border: 1px solid var(--qa-border);
    border-radius: 0.75rem;
    padding: 0.5rem 0.75rem;
    background: transparent;
    cursor: pointer;
  }
  .qa-chip--active {
    outline: 2px solid rgba(0, 0, 0, 0.1);
  }
  .qa-dark .qa-chip--active {
    outline: 2px solid rgba(255, 255, 255, 0.15);
  }
  .qa-hint {
    font-size: 0.75rem;
    color: var(--qa-muted);
  }

  .qa-lang-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
  .qa-lang-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    border: 1px solid var(--qa-border);
    border-radius: 0.75rem;
    padding: 0.5rem 0.75rem;
    background: transparent;
    cursor: pointer;
  }
  .qa-lang-item--active {
    outline: 2px solid rgba(0, 0, 0, 0.1);
  }
  .qa-dark .qa-lang-item--active {
    outline: 2px solid rgba(255, 255, 255, 0.15);
  }
  .qa-lang-label {
    font-weight: 500;
  }
  .qa-lang-code {
    font-size: 0.75rem;
    color: var(--qa-muted);
  }

  .qa-gh {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
  .qa-ev {
    display: block;
    border: 1px solid var(--qa-border);
    border-radius: 0.75rem;
    padding: 0.6rem 0.75rem;
    text-decoration: none;
    color: inherit;
  }
  .qa-ev:hover {
    background: rgba(0, 0, 0, 0.04);
  }
  .qa-ev-title {
    font-size: 0.9rem;
    font-weight: 500;
  }
  .qa-ev-sub {
    font-size: 0.75rem;
    color: var(--qa-muted);
  }
  .qa-error {
    color: #ef4444;
    font-size: 0.875rem;
  }
  .qa-muted {
    color: var(--qa-muted);
    font-size: 0.875rem;
  }

  .qa-shortcuts {
    position: absolute;
    left: 1rem;
    bottom: 1rem;
    color: var(--qa-muted);
    font-size: 0.75rem;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    pointer-events: none;
  }
  .qa-short {
    display: flex;
    gap: 0.75rem;
    align-items: center;
  }
  .qa-keys kbd {
    border: 1px solid var(--qa-border);
    padding: 0.1rem 0.35rem;
    border-radius: 0.375rem;
    background: transparent;
  }
</style>
